/**
 * Matching Mayhem Game Controller
 * Server-side game logic for Matching Mayhem
 */

import type { Server, Socket } from 'socket.io';
import { GameService } from '../../services/gameService';
import { logger } from '../../utils/logger';
import { GAME_TYPES } from '../../utils/constants';
import { MATCHING_MAYHEM_CONFIG } from '../../utils/matchingMayhemConstants';
import type { GameInitResult } from '../../types/game';
import type {
  MatchingMayhemGameState,
  RoundData,
  CardData,
  CardSelectActionData,
  CardSelectResult,
  MatchingMayhemStartData,
  MatchingMayhemGameStartedData,
  MatchingMayhemActionResultData
} from '../../types/matchingMayhem';

export class MatchingMayhemController {
  private gameService: GameService;
  private gameStates: Map<string, MatchingMayhemGameState> = new Map();
  private roundTimers: Map<string, NodeJS.Timeout> = new Map();
  private roundTickTimers: Map<string, NodeJS.Timeout> = new Map();
  private socketMap: Map<string, Socket> = new Map(); // Store socket references by roomId

  constructor(gameService: GameService) {
    this.gameService = gameService;
  }

  /**
   * Initialize and start a new Matching Mayhem game session
   */
  initializeAndStartGame(roomId: string, socket: Socket): GameInitResult {
    // Check if room already has a game state
    let gameState = this.gameService.getGameState(roomId);

    if (gameState) {
      // Game state exists, check if we can start
      if (gameState.status === 'active') {
        return { success: false, message: 'Game is already active' };
      }
      if (gameState.status === 'ended') {
        return { success: false, message: 'Game session has ended - no restarts allowed' };
      }
    } else {
      // Create new game state
      gameState = this.gameService.createGameState(
        roomId, 
        GAME_TYPES.MATCHING_MAYHEM, 
        MATCHING_MAYHEM_CONFIG.LIVES.INITIAL_LIVES
      );
    }

    // Start the game
    const started = this.gameService.startGame(roomId, socket);
    if (!started) {
      return { success: false, message: 'Failed to start game' };
    }

    // Initialize Matching Mayhem specific game state
    const matchingMayhemState: MatchingMayhemGameState = {
      currentRound: null,
      roundsCompleted: 0,
      isRoundActive: false,
      roundStartTime: null
    };

    this.gameStates.set(roomId, matchingMayhemState);

    // Store socket reference for timer updates
    this.socketMap.set(roomId, socket);

    // Generate the first round
    const firstRound = this.generateNewRound(roomId, 1);
    if (!firstRound) {
      return { success: false, message: 'Failed to generate first round' };
    }

    // Start the first round
    this.startRound(roomId, firstRound);

    logger.info(`Matching Mayhem game initialized and started in room ${roomId}`);
    return { success: true, gameState };
  }

  /**
   * Generate a new round with cards
   */
  private generateNewRound(roomId: string, roundNumber: number): RoundData | null {
    try {
      // Choose a random animal and color for the main card
      const correctAnimalIndex = Math.floor(Math.random() * MATCHING_MAYHEM_CONFIG.ANIMAL_COUNT);
      const correctColorIndex = Math.floor(Math.random() * MATCHING_MAYHEM_CONFIG.COLOR_COUNT);

      // Create main card data
      const mainCard = {
        animalIndex: correctAnimalIndex,
        colorIndex: correctColorIndex,
        imageKey: `image_${correctColorIndex}_${correctAnimalIndex}`
      };

      // Choose a random position for the correct card (not center)
      let correctCardIndex: number;
      do {
        correctCardIndex = Math.floor(Math.random() * MATCHING_MAYHEM_CONFIG.CARD_COUNT);
      } while (correctCardIndex === MATCHING_MAYHEM_CONFIG.CENTER_CARD_INDEX);

      // Generate cards
      const cards: CardData[] = [];
      const usedAnimalIndices = new Set<number>();
      usedAnimalIndices.add(correctAnimalIndex);

      // Get available animal indices for distractors
      const availableAnimalIndices = Array.from(
        { length: MATCHING_MAYHEM_CONFIG.ANIMAL_COUNT },
        (_, index) => index
      ).filter(index => index !== correctAnimalIndex);

      // Shuffle available animals
      for (let i = availableAnimalIndices.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [availableAnimalIndices[i], availableAnimalIndices[j]] = [availableAnimalIndices[j], availableAnimalIndices[i]];
      }

      for (let i = 0; i < MATCHING_MAYHEM_CONFIG.CARD_COUNT; i++) {
        if (i === correctCardIndex) {
          // This is the correct card - same animal, different color
          let cardColorIndex: number;
          do {
            cardColorIndex = Math.floor(Math.random() * MATCHING_MAYHEM_CONFIG.COLOR_COUNT);
          } while (cardColorIndex === correctColorIndex);

          cards.push({
            animalIndex: correctAnimalIndex,
            colorIndex: cardColorIndex,
            imageKey: `image_${cardColorIndex}_${correctAnimalIndex}`,
            isCorrect: true,
            position: i
          });
        } else if (i === MATCHING_MAYHEM_CONFIG.CENTER_CARD_INDEX) {
          // Center card - exact match to main card (distractor)
          cards.push({
            animalIndex: correctAnimalIndex,
            colorIndex: correctColorIndex,
            imageKey: `image_${correctColorIndex}_${correctAnimalIndex}`,
            isCorrect: false,
            position: i
          });
        } else {
          // Regular distractor card - different animal
          const distractorAnimalIndex = availableAnimalIndices.length > 0 
            ? availableAnimalIndices.pop()! 
            : Array.from(usedAnimalIndices).find(index => index !== correctAnimalIndex) || 0;

          const distractorColorIndex = Math.floor(Math.random() * MATCHING_MAYHEM_CONFIG.COLOR_COUNT);

          cards.push({
            animalIndex: distractorAnimalIndex,
            colorIndex: distractorColorIndex,
            imageKey: `image_${distractorColorIndex}_${distractorAnimalIndex}`,
            isCorrect: false,
            position: i
          });

          usedAnimalIndices.add(distractorAnimalIndex);
        }
      }

      const roundData: RoundData = {
        roundNumber,
        correctCardIndex,
        mainCard,
        cards,
        startTime: Date.now(),
        timeLimit: MATCHING_MAYHEM_CONFIG.ROUND_TIME
      };

      logger.info(`Generated round ${roundNumber} for room ${roomId}, correct card at position ${correctCardIndex}`);
      return roundData;
    } catch (error) {
      logger.error(`Error generating round for room ${roomId}:`, error);
      return null;
    }
  }

  /**
   * Start a round
   */
  private startRound(roomId: string, roundData: RoundData): void {
    const matchingMayhemState = this.gameStates.get(roomId);
    const socket = this.socketMap.get(roomId);

    if (!matchingMayhemState || !socket) {
      logger.error(`No Matching Mayhem state or socket found for room ${roomId}`);
      return;
    }

    matchingMayhemState.currentRound = roundData;
    matchingMayhemState.isRoundActive = true;
    matchingMayhemState.roundStartTime = Date.now();

    // Clear any existing timers
    this.clearRoundTimers(roomId);

    // Set up round timer for timeout
    const timeoutTimer = setTimeout(() => {
      this.handleRoundTimeout(roomId);
    }, MATCHING_MAYHEM_CONFIG.ROUND_TIME);

    this.roundTimers.set(roomId, timeoutTimer);

    // Set up timer tick updates (every 100ms for smooth progress)
    let remainingTime = MATCHING_MAYHEM_CONFIG.ROUND_TIME;
    const tickInterval = 100; // 100ms intervals

    const tickTimer = setInterval(() => {
      remainingTime -= tickInterval;

      if (remainingTime <= 0 || !this.gameStates.get(roomId)?.isRoundActive) {
        clearInterval(tickTimer);
        this.roundTickTimers.delete(roomId);
        return;
      }

      // Send timer update to client
      socket.emit('round_timer_tick', {
        remainingTime,
        totalTime: MATCHING_MAYHEM_CONFIG.ROUND_TIME,
        progress: remainingTime / MATCHING_MAYHEM_CONFIG.ROUND_TIME
      });
    }, tickInterval);

    this.roundTickTimers.set(roomId, tickTimer);

    logger.info(`Started round ${roundData.roundNumber} in room ${roomId} with timer updates`);
  }

  /**
   * Clear all timers for a room
   */
  private clearRoundTimers(roomId: string): void {
    // Clear timeout timer
    const timeoutTimer = this.roundTimers.get(roomId);
    if (timeoutTimer) {
      clearTimeout(timeoutTimer);
      this.roundTimers.delete(roomId);
    }

    // Clear tick timer
    const tickTimer = this.roundTickTimers.get(roomId);
    if (tickTimer) {
      clearInterval(tickTimer);
      this.roundTickTimers.delete(roomId);
    }
  }

  /**
   * Handle round timeout
   */
  private handleRoundTimeout(roomId: string): void {
    const matchingMayhemState = this.gameStates.get(roomId);
    if (!matchingMayhemState || !matchingMayhemState.isRoundActive) {
      return;
    }

    logger.info(`Round timeout in room ${roomId}`);

    // Clear all timers for this round
    this.clearRoundTimers(roomId);

    // Deduct life for timeout
    const livesResult = this.gameService.deductLife(roomId);

    if (livesResult.gameEnded) {
      this.endGame(roomId, 'no_lives');
    } else {
      // Start next round
      this.setupNextRound(roomId);
    }
  }

  /**
   * Setup next round
   */
  private setupNextRound(roomId: string): void {
    const matchingMayhemState = this.gameStates.get(roomId);
    if (!matchingMayhemState) {
      return;
    }

    matchingMayhemState.isRoundActive = false;
    matchingMayhemState.roundsCompleted++;

    // Generate and start next round
    const nextRound = this.generateNewRound(roomId, matchingMayhemState.roundsCompleted + 1);
    if (nextRound) {
      setTimeout(() => {
        this.startRound(roomId, nextRound);
      }, 500); // Small delay between rounds
    }
  }

  /**
   * End the game
   */
  private endGame(roomId: string, reason: 'no_lives' | 'timeout' | 'manual'): void {
    const gameState = this.gameService.getGameState(roomId);
    const socket = this.socketMap.get(roomId);

    // Clear all timers
    this.clearRoundTimers(roomId);

    // End game in GameService
    this.gameService.endGame(roomId, reason);

    // Emit ended event to client if socket is available
    if (socket) {
      socket.emit('ended', {
        reason,
        finalScore: gameState?.score || 0
      });
    }

    // Clear game state and socket reference
    this.gameStates.delete(roomId);
    this.socketMap.delete(roomId);

    logger.info(`Matching Mayhem game ended in room ${roomId}, reason: ${reason}, final score: ${gameState?.score || 0}`);
  }

  /**
   * Handle card selection
   */
  handleCardSelection(roomId: string, cardIndex: number, _reactionTime: number = 0): CardSelectResult {
    const gameState = this.gameService.getGameState(roomId);
    const matchingMayhemState = this.gameStates.get(roomId);

    if (!gameState || !matchingMayhemState) {
      return {
        success: false,
        isCorrect: false,
        points: 0,
        newScore: 0,
        newLives: 0,
        gameEnded: true,
        correctCardIndex: -1
      };
    }

    const currentRound = matchingMayhemState.currentRound;
    if (!currentRound || !matchingMayhemState.isRoundActive) {
      return {
        success: false,
        isCorrect: false,
        points: 0,
        newScore: gameState.score,
        newLives: gameState.lives,
        gameEnded: false,
        correctCardIndex: currentRound?.correctCardIndex || -1
      };
    }

      // Clear all round timers since round is ending
    this.clearRoundTimers(roomId);

    const isCorrect = cardIndex === currentRound.correctCardIndex;
    // Flag to track if we need to set up next round
    let setupNextRound = false;

    if (isCorrect) {
      // CORRECT ANSWER
      // Calculate points based on reaction time
      const timeElapsed = Date.now() - (matchingMayhemState.roundStartTime || Date.now());
      const timeRemaining = Math.max(0, MATCHING_MAYHEM_CONFIG.ROUND_TIME - timeElapsed);
      const timePercentage = timeRemaining / MATCHING_MAYHEM_CONFIG.ROUND_TIME;
      setupNextRound = true;      // Calculate score: higher percentage for faster response
      const points = Math.floor(
        MATCHING_MAYHEM_CONFIG.SCORING.MIN_ROUND_SCORE +
        (MATCHING_MAYHEM_CONFIG.SCORING.MAX_ROUND_SCORE - MATCHING_MAYHEM_CONFIG.SCORING.MIN_ROUND_SCORE) * timePercentage
      );

      // Update score
      this.gameService.updateScore(roomId, points, "add");

      logger.info(`Correct card selection in room ${roomId}: card ${cardIndex}, points ${points}, new score ${gameState.score}`);

      // Setup next round
      this.setupNextRound(roomId);

      return {
        success: true,
        isCorrect: true,
        points,
        newScore: gameState.score,
        newLives: gameState.lives,
        gameEnded: false,
        correctCardIndex: currentRound.correctCardIndex
      };
    } else {
      // WRONG ANSWER
      // Apply penalty
      const penalty = MATCHING_MAYHEM_CONFIG.SCORING.WRONG_ANSWER_PENALTY;
      this.gameService.updateScore(roomId, penalty, "subtract");

      // Deduct life
      const livesResult = this.gameService.deductLife(roomId);

      // Check if game should end
      if (livesResult.gameEnded) {
        this.endGame(roomId, 'no_lives');
      } else {
        // If game hasn't ended, set up next round after a wrong answer
        setupNextRound = true;
      }

      logger.info(`Wrong card selection in room ${roomId}: card ${cardIndex}, penalty ${penalty}, new score ${gameState.score}, lives ${livesResult.newLives}, game ended: ${livesResult.gameEnded}`);

      if (setupNextRound) {
        this.setupNextRound(roomId);
      }

      return {
        success: true,
        isCorrect: false,
        points: penalty,
        newScore: gameState.score,
        newLives: livesResult.newLives,
        gameEnded: livesResult.gameEnded,
        correctCardIndex: currentRound.correctCardIndex
      };
    }
  }

  /**
   * Get current round data
   */
  getCurrentRound(roomId: string): RoundData | null {
    const matchingMayhemState = this.gameStates.get(roomId);
    return matchingMayhemState?.currentRound || null;
  }

  /**
   * Setup socket event handlers for Matching Mayhem
   */
  public setupSocketHandlers(_io: Server, socket: Socket): void {
    // Generic game start event
    socket.on('start', (data) => {
      if (data.gameId === GAME_TYPES.MATCHING_MAYHEM) {
        this.handleGameStart(socket, data);
      }
    });

    // Generic game end event
    socket.on('end', (data) => {
      if (data.gameId === GAME_TYPES.MATCHING_MAYHEM) {
        this.handleGameEnd(socket, data);
      }
    });

    // Generic game action event (for game-specific actions)
    socket.on('action', (data) => {
      if (data.gameId === GAME_TYPES.MATCHING_MAYHEM) {
        this.handleGameAction(socket, data);
      }
    });
  }

  /**
   * Handle game start
   */
  private handleGameStart(socket: Socket, data: MatchingMayhemStartData): void {
    const { roomId, gameId } = data;

    if (!roomId || !gameId) {
      socket.emit('error', {
        message: 'Missing required data for game start'
      });
      return;
    }

    try {
      // Initialize and start the game
      const result = this.initializeAndStartGame(roomId, socket);

      if (result.success && result.gameState) {
        // Get first round data
        const firstRound = this.getCurrentRound(roomId);

        if (firstRound) {
          const gameStartedData: MatchingMayhemGameStartedData = {
            gameState: {
              score: result.gameState.score,
              lives: result.gameState.lives,
              isActive: result.gameState.status === 'active',
              startTime: result.gameState.startTime
            },
            firstRound,
            message: 'Game started!'
          };

          socket.emit('started', gameStartedData);
          logger.info(`${gameId} game started in room ${roomId}`);
        } else {
          socket.emit('error', {
            message: 'Failed to generate first round'
          });
        }
      } else {
        socket.emit('error', {
          message: result.message || 'Failed to start game'
        });
      }
    } catch (error) {
      logger.error(`Error starting ${gameId} game in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Handle game end
   */
  private handleGameEnd(socket: Socket, data: any): void {
    const { roomId, gameId, reason } = data;

    if (!roomId || !gameId) {
      socket.emit('error', {
        message: 'Missing required data for game end'
      });
      return;
    }

    try {
      this.endGame(roomId, reason || 'manual');
      // Note: endGame() method already emitted the 'ended' event

      logger.info(`${gameId} game ended in room ${roomId}, reason: ${reason || 'manual'}`);
    } catch (error) {
      logger.error(`Error ending ${gameId} game in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Handle game action
   */
  private handleGameAction(socket: Socket, data: any): void {
    const { roomId, gameId, action } = data;

    if (!roomId || !gameId || !action) {
      socket.emit('error', {
        message: 'Missing required data for game action'
      });
      return;
    }

    try {
      // Handle specific action types for Matching Mayhem
      switch (action.type) {
        case 'card_select':
          this.handleCardSelectAction(socket, data as CardSelectActionData);
          break;
        default:
          socket.emit('error', {
            message: `Unknown action type: ${action.type}`
          });
      }
    } catch (error) {
      logger.error(`Error processing game action in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Handle card select action
   */
  private handleCardSelectAction(socket: Socket, data: CardSelectActionData): void {
    const { roomId, gameId, action } = data;
    const { cardIndex, reactionTime, clickTime } = action.data;

    if (!roomId || cardIndex === undefined || !gameId) {
      socket.emit('error', {
        message: 'Missing required data for card select action'
      });
      return;
    }

    try {
      // Validate the click timing
      const currentTime = Date.now();
      const timeDiff = Math.abs(currentTime - (clickTime || currentTime));
      if (timeDiff > 5000) { // 5 seconds tolerance
        socket.emit('error', {
          message: 'Click time too far from current time'
        });
        return;
      }

      // Process the card selection
      const result = this.handleCardSelection(roomId, cardIndex, reactionTime || 0);

      if (result.success) {
        // Get next round if available
        const nextRound = result.gameEnded ? undefined : this.getCurrentRound(roomId) || undefined;

        const actionResultData: MatchingMayhemActionResultData = {
          actionType: 'card_select',
          data: {
            cardIndex,
            isCorrect: result.isCorrect,
            points: result.points,
            newScore: result.newScore,
            newLives: result.newLives,
            gameEnded: result.gameEnded,
            nextRound,
            correctCardIndex: result.correctCardIndex
          }
        };

        // Send action result to client
        socket.emit('action_result', actionResultData);

        // Note: If game ended, the endGame() method already emitted the 'ended' event

        logger.info(`Card select action processed in room ${roomId}: card ${cardIndex}, correct: ${result.isCorrect}, score: ${result.newScore}`);
      } else {
        socket.emit('error', {
          message: 'Failed to process card selection'
        });
      }
    } catch (error) {
      logger.error(`Error processing card select action in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }
}
